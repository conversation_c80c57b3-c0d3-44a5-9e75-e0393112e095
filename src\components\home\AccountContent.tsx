'use client';

import React, { Suspense } from 'react';
import { usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';

// Dynamically import account page components to avoid layout conflicts
const AccountDashboard = dynamic(() => import('@/app/account/dashboard/AccountDashboardContent'), {
  loading: () => <div className="flex items-center justify-center min-h-96"><div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div></div>
});

// For now, let's create a simple placeholder for other pages until we can properly extract their content
const AccountPagePlaceholder = ({ title, description }: { title: string; description: string }) => (
  <div className="space-y-6">
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
      <p className="text-gray-600">{description}</p>
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-blue-800 text-sm">
          This page content will be fully integrated into the SPA structure.
          For now, you can access the full functionality by visiting the direct URL.
        </p>
      </div>
    </div>
  </div>
);

export default function AccountContent() {
  const pathname = usePathname();

  // Render the appropriate account content based on the current path
  const renderAccountContent = () => {
    switch (pathname) {
      case '/account':
      case '/account/dashboard':
        return <AccountDashboard />;
      case '/account/profile':
        return <AccountPagePlaceholder title="Profile" description="Manage your personal information and preferences." />;
      case '/account/orders':
        return <AccountPagePlaceholder title="Order History" description="View and track your past orders." />;
      case '/account/orders/track':
        return <AccountPagePlaceholder title="Track Order" description="Track your current orders in real-time." />;
      case '/account/favorites':
        return <AccountPagePlaceholder title="Favorites" description="Your favorite restaurants and dishes." />;
      case '/account/reviews':
        return <AccountPagePlaceholder title="Reviews" description="Your reviews and ratings for restaurants." />;
      case '/account/addresses':
        return <AccountPagePlaceholder title="Addresses" description="Manage your delivery addresses." />;
      case '/account/payment':
        return <AccountPagePlaceholder title="Payment Methods" description="Manage your payment methods and billing." />;
      case '/account/loyalty':
        return <AccountPagePlaceholder title="Loyalty Points" description="View and redeem your loyalty points." />;
      case '/account/promotions':
        return <AccountPagePlaceholder title="Promotions" description="Available promotions and discounts." />;
      case '/account/referrals':
        return <AccountPagePlaceholder title="Referrals" description="Refer friends and earn rewards." />;
      case '/account/help':
        return <AccountPagePlaceholder title="Help Center" description="Find answers to frequently asked questions." />;
      case '/account/support':
        return <AccountPagePlaceholder title="Contact Support" description="Get help from our support team." />;
      case '/account/notifications':
        return <AccountPagePlaceholder title="Notifications" description="Manage your notification preferences." />;
      case '/account/settings':
        return <AccountPagePlaceholder title="Settings" description="Account settings and preferences." />;
      default:
        return <AccountDashboard />; // Default to dashboard
    }
  };

  return (
    <div className="space-y-8">
      {renderAccountContent()}
    </div>
  );
}
