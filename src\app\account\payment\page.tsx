'use client';

// /account/payment route - redirects to main homepage with account view
import Home from '../../page';

export default function AccountPaymentPage() {
  return <Home />;
}
import {
  CreditCardIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  ShieldCheckIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';

interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'paypal' | 'apple_pay' | 'google_pay';
  cardNumber: string;
  cardHolder: string;
  expiryDate: string;
  brand: string;
  isDefault: boolean;
  isVerified: boolean;
}

export default function PaymentMethods() {
  const [paymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'credit_card',
      cardNumber: '**** **** **** 1234',
      cardHolder: 'John <PERSON>',
      expiryDate: '12/26',
      brand: 'Visa',
      isDefault: true,
      isVerified: true,
    },
    {
      id: '2',
      type: 'credit_card',
      cardNumber: '**** **** **** 5678',
      cardHolder: '<PERSON>',
      expiryDate: '08/25',
      brand: 'Mastercard',
      isDefault: false,
      isVerified: true,
    },
    {
      id: '3',
      type: 'paypal',
      cardNumber: '<EMAIL>',
      cardHolder: 'John Lloyd Callao',
      expiryDate: '',
      brand: 'PayPal',
      isDefault: false,
      isVerified: true,
    },
  ]);

  const getCardIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return (
          <div className="w-8 h-5 bg-blue-600 rounded text-white text-xs font-bold flex items-center justify-center">
            VISA
          </div>
        );
      case 'mastercard':
        return (
          <div className="w-8 h-5 bg-red-500 rounded text-white text-xs font-bold flex items-center justify-center">
            MC
          </div>
        );
      case 'paypal':
        return (
          <div className="w-8 h-5 bg-blue-500 rounded text-white text-xs font-bold flex items-center justify-center">
            PP
          </div>
        );
      default:
        return <CreditCardIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'credit_card':
        return 'Credit Card';
      case 'debit_card':
        return 'Debit Card';
      case 'paypal':
        return 'PayPal';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      default:
        return 'Payment Method';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Methods</h1>
          <p className="text-gray-600">Manage your saved payment options.</p>
        </div>
        <button className="flex items-center space-x-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
          <PlusIcon className="h-4 w-4" />
          <span>Add Payment Method</span>
        </button>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <ShieldCheckIcon className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900">Your payment information is secure</h3>
            <p className="text-sm text-blue-700 mt-1">
              We use industry-standard encryption to protect your payment details. Your card information is never stored on our servers.
            </p>
          </div>
        </div>
      </div>

      {/* Payment Methods List */}
      <div className="space-y-4">
        {paymentMethods.map((method) => (
          <div key={method.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  {getCardIcon(method.brand)}
                </div>
                
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-semibold text-gray-900">{method.brand} {getTypeLabel(method.type)}</h3>
                    {method.isDefault && (
                      <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">
                        Default
                      </span>
                    )}
                    {method.isVerified && (
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600">
                    <p className="font-medium text-gray-900">{method.cardHolder}</p>
                    <div className="flex items-center space-x-4">
                      <span>{method.cardNumber}</span>
                      {method.expiryDate && <span>Expires {method.expiryDate}</span>}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {!method.isDefault && (
                  <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">
                    Set Default
                  </button>
                )}
                <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Payment Method Options */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Payment Method</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors">
            <CreditCardIcon className="h-6 w-6 text-gray-600" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Credit/Debit Card</p>
              <p className="text-sm text-gray-500">Visa, Mastercard, etc.</p>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors">
            <div className="w-6 h-6 bg-blue-500 rounded text-white text-xs font-bold flex items-center justify-center">
              PP
            </div>
            <div className="text-left">
              <p className="font-medium text-gray-900">PayPal</p>
              <p className="text-sm text-gray-500">Pay with your PayPal account</p>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors">
            <BanknotesIcon className="h-6 w-6 text-gray-600" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Digital Wallet</p>
              <p className="text-sm text-gray-500">Apple Pay, Google Pay</p>
            </div>
          </button>
        </div>
      </div>

      {/* Payment Settings */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">Save payment methods</p>
              <p className="text-sm text-gray-500">Securely save your payment methods for faster checkout</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">Auto-pay for subscriptions</p>
              <p className="text-sm text-gray-500">Automatically pay for recurring orders and subscriptions</p>
            </div>
            <input
              type="checkbox"
              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">Payment notifications</p>
              <p className="text-sm text-gray-500">Get notified about payment confirmations and failures</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            />
          </div>
        </div>
      </div>

      {/* Billing History */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
          <button className="text-orange-600 hover:text-orange-700 font-medium text-sm">
            View All
          </button>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Pizza Palace</p>
                <p className="text-sm text-gray-500">Jan 15, 2024 • Visa ****1234</p>
              </div>
            </div>
            <span className="font-semibold text-gray-900">$32.36</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Burger Junction</p>
                <p className="text-sm text-gray-500">Jan 14, 2024 • PayPal</p>
              </div>
            </div>
            <span className="font-semibold text-gray-900">$42.07</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Sushi Zen</p>
                <p className="text-sm text-gray-500">Jan 12, 2024 • Mastercard ****5678</p>
              </div>
            </div>
            <span className="font-semibold text-gray-900">$38.95</span>
          </div>
        </div>
      </div>

      {paymentMethods.length === 0 && (
        <div className="text-center py-12">
          <CreditCardIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No payment methods saved</h3>
          <p className="text-gray-500 mb-6">Add your first payment method for faster checkout.</p>
          <button className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
            Add Payment Method
          </button>
        </div>
      )}
    </div>
  );
}
