'use client';

// /account/promotions route - redirects to main homepage with account view
import Home from '../../page';

export default function AccountPromotionsPage() {
  return <Home />;
}
import {
  GiftIcon,
  TagIcon,
  ClockIcon,
  CheckCircleIcon,
  DocumentDuplicateIcon,
  CalendarIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

interface Promotion {
  id: string;
  title: string;
  description: string;
  code: string;
  discount: string;
  type: 'percentage' | 'fixed' | 'free_delivery' | 'bogo';
  minOrder?: number;
  maxDiscount?: number;
  validUntil: Date;
  isActive: boolean;
  isUsed: boolean;
  usageCount?: number;
  maxUsage?: number;
  restaurants?: string[];
}

export default function Promotions() {
  const [activeTab, setActiveTab] = useState<'available' | 'used' | 'expired'>('available');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const [promotions] = useState<Promotion[]>([
    {
      id: '1',
      title: '20% Off Your Next Order',
      description: 'Get 20% discount on orders over $25. Valid at all restaurants.',
      code: 'SAVE20',
      discount: '20% off',
      type: 'percentage',
      minOrder: 25,
      maxDiscount: 10,
      validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      isActive: true,
      isUsed: false,
      maxUsage: 1,
    },
    {
      id: '2',
      title: 'Free Delivery Weekend',
      description: 'Enjoy free delivery on all orders this weekend. No minimum order required.',
      code: 'FREEDEL',
      discount: 'Free delivery',
      type: 'free_delivery',
      validUntil: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
      isActive: true,
      isUsed: false,
      maxUsage: 3,
      usageCount: 1,
    },
    {
      id: '3',
      title: '$5 Off Pizza Orders',
      description: 'Get $5 off when you order from any pizza restaurant.',
      code: 'PIZZA5',
      discount: '$5 off',
      type: 'fixed',
      minOrder: 20,
      validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      isActive: true,
      isUsed: false,
      restaurants: ['Pizza Palace', 'Tony\'s Pizza'],
    },
    {
      id: '4',
      title: 'Buy One Get One Free',
      description: 'Buy one burger, get another one free at Burger Junction.',
      code: 'BOGO',
      discount: 'BOGO',
      type: 'bogo',
      validUntil: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      isActive: true,
      isUsed: false,
      restaurants: ['Burger Junction'],
    },
    {
      id: '5',
      title: 'Welcome Bonus',
      description: '15% off your first order. Welcome to Tap2Go!',
      code: 'WELCOME15',
      discount: '15% off',
      type: 'percentage',
      minOrder: 15,
      validUntil: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago (used)
      isActive: false,
      isUsed: true,
      maxUsage: 1,
      usageCount: 1,
    },
  ]);

  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return <TagIcon className="h-5 w-5" />;
      case 'fixed':
        return <GiftIcon className="h-5 w-5" />;
      case 'free_delivery':
        return <ClockIcon className="h-5 w-5" />;
      case 'bogo':
        return <StarIcon className="h-5 w-5" />;
      default:
        return <GiftIcon className="h-5 w-5" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'percentage':
        return 'bg-blue-100 text-blue-600';
      case 'fixed':
        return 'bg-green-100 text-green-600';
      case 'free_delivery':
        return 'bg-purple-100 text-purple-600';
      case 'bogo':
        return 'bg-orange-100 text-orange-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysRemaining = (date: Date) => {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const filteredPromotions = promotions.filter(promo => {
    switch (activeTab) {
      case 'available':
        return promo.isActive && !promo.isUsed;
      case 'used':
        return promo.isUsed;
      case 'expired':
        return !promo.isActive && !promo.isUsed;
      default:
        return true;
    }
  });

  const availableCount = promotions.filter(p => p.isActive && !p.isUsed).length;
  const usedCount = promotions.filter(p => p.isUsed).length;
  const expiredCount = promotions.filter(p => !p.isActive && !p.isUsed).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Promotions & Offers</h1>
          <p className="text-gray-600">Save money with exclusive deals and discount codes.</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Available</p>
              <p className="text-3xl font-bold text-green-600">{availableCount}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <GiftIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Used</p>
              <p className="text-3xl font-bold text-blue-600">{usedCount}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <CheckCircleIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Expired</p>
              <p className="text-3xl font-bold text-gray-600">{expiredCount}</p>
            </div>
            <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
              <ClockIcon className="h-6 w-6 text-gray-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('available')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'available'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Available ({availableCount})
          </button>
          <button
            onClick={() => setActiveTab('used')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'used'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Used ({usedCount})
          </button>
          <button
            onClick={() => setActiveTab('expired')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'expired'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Expired ({expiredCount})
          </button>
        </nav>
      </div>

      {/* Promotions List */}
      <div className="space-y-4">
        {filteredPromotions.map((promo) => {
          const daysRemaining = getDaysRemaining(promo.validUntil);
          const isExpiringSoon = daysRemaining <= 3 && daysRemaining > 0;
          
          return (
            <div key={promo.id} className={`bg-white rounded-xl shadow-sm border p-6 ${
              promo.isActive && !promo.isUsed 
                ? 'border-gray-100 hover:shadow-md transition-shadow' 
                : 'border-gray-200 opacity-75'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${getTypeColor(promo.type)}`}>
                    {getTypeIcon(promo.type)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-gray-900">{promo.title}</h3>
                      {isExpiringSoon && (
                        <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                          Expires Soon
                        </span>
                      )}
                      {promo.isUsed && (
                        <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full font-medium">
                          Used
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{promo.description}</p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <CalendarIcon className="h-4 w-4" />
                        <span>
                          {daysRemaining > 0 
                            ? `${daysRemaining} days left`
                            : `Expired ${formatDate(promo.validUntil)}`
                          }
                        </span>
                      </div>
                      
                      {promo.minOrder && (
                        <span>Min order: ${promo.minOrder}</span>
                      )}
                      
                      {promo.maxDiscount && (
                        <span>Max discount: ${promo.maxDiscount}</span>
                      )}
                      
                      {promo.maxUsage && (
                        <span>
                          {promo.usageCount || 0}/{promo.maxUsage} uses
                        </span>
                      )}
                    </div>
                    
                    {promo.restaurants && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          Valid at: {promo.restaurants.join(', ')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="mb-3">
                    <span className="text-2xl font-bold text-orange-600">{promo.discount}</span>
                  </div>
                  
                  {promo.isActive && !promo.isUsed ? (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-3 py-2">
                        <code className="font-mono text-sm font-medium">{promo.code}</code>
                        <button
                          onClick={() => copyCode(promo.code)}
                          className="p-1 hover:bg-gray-200 rounded transition-colors"
                          title="Copy code"
                        >
                          {copiedCode === promo.code ? (
                            <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          ) : (
                            <DocumentDuplicateIcon className="h-4 w-4 text-gray-600" />
                          )}
                        </button>
                      </div>
                      <button className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                        Use Now
                      </button>
                    </div>
                  ) : (
                    <div className="bg-gray-100 rounded-lg px-3 py-2">
                      <code className="font-mono text-sm text-gray-500">{promo.code}</code>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredPromotions.length === 0 && (
        <div className="text-center py-12">
          <GiftIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No {activeTab} promotions
          </h3>
          <p className="text-gray-500">
            {activeTab === 'available' 
              ? 'Check back later for new deals and offers!'
              : `You don't have any ${activeTab} promotions.`
            }
          </p>
        </div>
      )}

      {/* How to Use */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 className="font-medium text-blue-900 mb-3">💡 How to use promo codes</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p><strong>1.</strong> Copy the promo code by clicking the copy button</p>
          <p><strong>2.</strong> Add items to your cart and proceed to checkout</p>
          <p><strong>3.</strong> Paste the code in the "Promo Code" field</p>
          <p><strong>4.</strong> Click "Apply" to see your discount</p>
          <p><strong>5.</strong> Complete your order to enjoy the savings!</p>
        </div>
      </div>
    </div>
  );
}
