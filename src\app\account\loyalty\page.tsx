'use client';

// /account/loyalty route - redirects to main homepage with account view
import Home from '../../page';

export default function AccountLoyaltyPage() {
  return <Home />;
}
import {
  GiftIcon,
  StarIcon,
  TrophyIcon,
  FireIcon,
  ClockIcon,
  ArrowRightIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface LoyaltyData {
  currentPoints: number;
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  nextTier: string;
  pointsToNextTier: number;
  totalEarned: number;
  totalRedeemed: number;
  memberSince: Date;
}

interface Reward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  category: 'discount' | 'free_delivery' | 'free_item' | 'cashback';
  validUntil?: Date;
  isAvailable: boolean;
}

interface PointsHistory {
  id: string;
  type: 'earned' | 'redeemed' | 'expired';
  points: number;
  description: string;
  date: Date;
  orderId?: string;
}

export default function LoyaltyProgram() {
  const [loyaltyData] = useState<LoyaltyData>({
    currentPoints: 2340,
    tier: 'Gold',
    nextTier: 'Platinum',
    pointsToNextTier: 660,
    totalEarned: 5680,
    totalRedeemed: 3340,
    memberSince: new Date('2023-03-15'),
  });

  const [rewards] = useState<Reward[]>([
    {
      id: '1',
      title: '20% Off Next Order',
      description: 'Get 20% discount on your next order (max $10)',
      pointsCost: 500,
      category: 'discount',
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      isAvailable: true,
    },
    {
      id: '2',
      title: 'Free Delivery',
      description: 'Free delivery on your next 3 orders',
      pointsCost: 300,
      category: 'free_delivery',
      isAvailable: true,
    },
    {
      id: '3',
      title: 'Free Dessert',
      description: 'Get a free dessert with any order over $25',
      pointsCost: 800,
      category: 'free_item',
      validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
      isAvailable: true,
    },
    {
      id: '4',
      title: '$5 Cashback',
      description: 'Get $5 credited to your account',
      pointsCost: 1000,
      category: 'cashback',
      isAvailable: true,
    },
    {
      id: '5',
      title: '50% Off Premium Delivery',
      description: 'Half price on express delivery for 1 month',
      pointsCost: 1500,
      category: 'discount',
      isAvailable: false, // Not enough points
    },
  ]);

  const [pointsHistory] = useState<PointsHistory[]>([
    {
      id: '1',
      type: 'earned',
      points: 32,
      description: 'Order from Pizza Palace',
      date: new Date(Date.now() - 2 * 60 * 60 * 1000),
      orderId: 'ORD-2024-001',
    },
    {
      id: '2',
      type: 'redeemed',
      points: -500,
      description: 'Redeemed 20% off coupon',
      date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    },
    {
      id: '3',
      type: 'earned',
      points: 42,
      description: 'Order from Burger Junction',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      orderId: 'ORD-2024-002',
    },
    {
      id: '4',
      type: 'earned',
      points: 15,
      description: 'Bonus points for review',
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    },
  ]);

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze':
        return 'text-orange-600 bg-orange-100';
      case 'Silver':
        return 'text-gray-600 bg-gray-100';
      case 'Gold':
        return 'text-yellow-600 bg-yellow-100';
      case 'Platinum':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'Bronze':
        return <TrophyIcon className="h-5 w-5 text-orange-600" />;
      case 'Silver':
        return <TrophyIcon className="h-5 w-5 text-gray-600" />;
      case 'Gold':
        return <TrophyIcon className="h-5 w-5 text-yellow-600" />;
      case 'Platinum':
        return <TrophyIcon className="h-5 w-5 text-purple-600" />;
      default:
        return <TrophyIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getRewardIcon = (category: string) => {
    switch (category) {
      case 'discount':
        return <StarIcon className="h-5 w-5" />;
      case 'free_delivery':
        return <GiftIcon className="h-5 w-5" />;
      case 'free_item':
        return <GiftIcon className="h-5 w-5" />;
      case 'cashback':
        return <FireIcon className="h-5 w-5" />;
      default:
        return <GiftIcon className="h-5 w-5" />;
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return date.toLocaleDateString();
  };

  const progressPercentage = ((loyaltyData.currentPoints / (loyaltyData.currentPoints + loyaltyData.pointsToNextTier)) * 100);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Loyalty Program</h1>
          <p className="text-gray-600">Earn points with every order and unlock exclusive rewards.</p>
        </div>
      </div>

      {/* Loyalty Status Card */}
      <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getTierIcon(loyaltyData.tier)}
            <div>
              <h2 className="text-2xl font-bold">{loyaltyData.tier} Member</h2>
              <p className="text-orange-100">Member since {loyaltyData.memberSince.toLocaleDateString()}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-3xl font-bold">{loyaltyData.currentPoints.toLocaleString()}</p>
            <p className="text-orange-100">Points Available</p>
          </div>
        </div>
        
        <div className="bg-white/20 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">Progress to {loyaltyData.nextTier}</span>
            <span className="text-sm">{loyaltyData.pointsToNextTier} points to go</span>
          </div>
          <div className="w-full bg-white/30 rounded-full h-2">
            <div 
              className="bg-white h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Earned</p>
              <p className="text-2xl font-bold text-gray-900">{loyaltyData.totalEarned.toLocaleString()}</p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <TrophyIcon className="h-5 w-5 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Redeemed</p>
              <p className="text-2xl font-bold text-gray-900">{loyaltyData.totalRedeemed.toLocaleString()}</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <GiftIcon className="h-5 w-5 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Current Balance</p>
              <p className="text-2xl font-bold text-gray-900">{loyaltyData.currentPoints.toLocaleString()}</p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <StarIcon className="h-5 w-5 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Available Rewards */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Rewards</h3>
          <div className="space-y-4">
            {rewards.map((reward) => (
              <div key={reward.id} className={`border rounded-lg p-4 ${
                reward.isAvailable ? 'border-gray-200 hover:border-orange-300' : 'border-gray-100 bg-gray-50'
              }`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      reward.isAvailable ? 'bg-orange-100 text-orange-600' : 'bg-gray-100 text-gray-400'
                    }`}>
                      {getRewardIcon(reward.category)}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-medium ${reward.isAvailable ? 'text-gray-900' : 'text-gray-500'}`}>
                        {reward.title}
                      </h4>
                      <p className={`text-sm mt-1 ${reward.isAvailable ? 'text-gray-600' : 'text-gray-400'}`}>
                        {reward.description}
                      </p>
                      {reward.validUntil && (
                        <p className="text-xs text-gray-500 mt-1">
                          Valid until {reward.validUntil.toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-bold ${reward.isAvailable ? 'text-gray-900' : 'text-gray-500'}`}>
                      {reward.pointsCost} pts
                    </p>
                    <button
                      disabled={!reward.isAvailable || loyaltyData.currentPoints < reward.pointsCost}
                      className={`mt-2 px-3 py-1 rounded text-sm font-medium transition-colors ${
                        reward.isAvailable && loyaltyData.currentPoints >= reward.pointsCost
                          ? 'bg-orange-600 hover:bg-orange-700 text-white'
                          : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      {loyaltyData.currentPoints >= reward.pointsCost ? 'Redeem' : 'Not enough points'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Points History */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Points History</h3>
            <button className="text-orange-600 hover:text-orange-700 font-medium text-sm">
              View All
            </button>
          </div>
          <div className="space-y-3">
            {pointsHistory.map((entry) => (
              <div key={entry.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    entry.type === 'earned' 
                      ? 'bg-green-100 text-green-600' 
                      : entry.type === 'redeemed'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-red-100 text-red-600'
                  }`}>
                    {entry.type === 'earned' ? (
                      <ArrowRightIcon className="h-4 w-4 rotate-180" />
                    ) : entry.type === 'redeemed' ? (
                      <GiftIcon className="h-4 w-4" />
                    ) : (
                      <ClockIcon className="h-4 w-4" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{entry.description}</p>
                    <p className="text-sm text-gray-500">{formatDate(entry.date)}</p>
                  </div>
                </div>
                <span className={`font-semibold ${
                  entry.type === 'earned' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {entry.type === 'earned' ? '+' : ''}{entry.points}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* How to Earn Points */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Earn Points</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <GiftIcon className="h-6 w-6 text-green-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Place Orders</h4>
            <p className="text-sm text-gray-600">Earn 1 point for every $1 spent</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <StarIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Write Reviews</h4>
            <p className="text-sm text-gray-600">Get 15 bonus points per review</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <TrophyIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Refer Friends</h4>
            <p className="text-sm text-gray-600">Earn 100 points per referral</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <FireIcon className="h-6 w-6 text-orange-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Special Events</h4>
            <p className="text-sm text-gray-600">Double points during promotions</p>
          </div>
        </div>
      </div>
    </div>
  );
}
