'use client';

// /account/support route - redirects to main homepage with account view
import Home from '../../page';

export default function AccountSupportPage() {
  return <Home />;
}
import {
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  EnvelopeIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  QuestionMarkCircleIcon,
  DocumentTextIcon,
  PaperAirplaneIcon,
} from '@heroicons/react/24/outline';

export default function ContactSupport() {
  const [selectedIssue, setSelectedIssue] = useState('');
  const [message, setMessage] = useState('');
  const [orderNumber, setOrderNumber] = useState('');
  const [priority, setPriority] = useState('normal');

  const issueTypes = [
    { id: 'order', label: 'Order Issue', description: 'Problems with your current or past orders' },
    { id: 'payment', label: 'Payment Problem', description: 'Billing, refunds, or payment method issues' },
    { id: 'delivery', label: 'Delivery Issue', description: 'Late delivery, missing items, or driver problems' },
    { id: 'account', label: 'Account Help', description: 'Login, profile, or account settings' },
    { id: 'technical', label: 'Technical Issue', description: 'App bugs, website problems, or technical difficulties' },
    { id: 'restaurant', label: 'Restaurant Complaint', description: 'Food quality, service, or restaurant-related issues' },
    { id: 'other', label: 'Other', description: 'Something else not listed above' },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Support request submitted:', {
      selectedIssue,
      message,
      orderNumber,
      priority,
    });
    // Here you would typically send the data to your backend
    alert('Your support request has been submitted. We\'ll get back to you soon!');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Contact Support</h1>
        <p className="text-gray-600">We're here to help! Choose how you'd like to get in touch.</p>
      </div>

      {/* Contact Methods */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow cursor-pointer">
          <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
            <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Live Chat</h3>
          <p className="text-sm text-gray-600 mb-3">Get instant help from our support team</p>
          <div className="flex items-center justify-center space-x-2 mb-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-green-600 font-medium">Available now</span>
          </div>
          <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
            Start Chat
          </button>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow cursor-pointer">
          <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
            <PhoneIcon className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Call Support</h3>
          <p className="text-sm text-gray-600 mb-3">Speak directly with our team</p>
          <p className="text-lg font-bold text-gray-900 mb-3">+****************</p>
          <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
            Call Now
          </button>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
          <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
            <EnvelopeIcon className="h-6 w-6 text-purple-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Email Support</h3>
          <p className="text-sm text-gray-600 mb-3">Send us a detailed message</p>
          <p className="text-sm text-gray-600 mb-3">Response within 24 hours</p>
          <p className="text-sm text-purple-600 font-medium">Use form below</p>
        </div>
      </div>

      {/* Support Form */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Send us a message</h3>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Issue Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">What can we help you with?</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {issueTypes.map((issue) => (
                <label
                  key={issue.id}
                  className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                    selectedIssue === issue.id
                      ? 'border-orange-300 bg-orange-50'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="issue-type"
                    value={issue.id}
                    checked={selectedIssue === issue.id}
                    onChange={(e) => setSelectedIssue(e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex flex-1">
                    <div className="flex flex-col">
                      <span className="block text-sm font-medium text-gray-900">{issue.label}</span>
                      <span className="block text-sm text-gray-500">{issue.description}</span>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Order Number (conditional) */}
          {(selectedIssue === 'order' || selectedIssue === 'delivery' || selectedIssue === 'payment') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Order Number (optional)
              </label>
              <input
                type="text"
                value={orderNumber}
                onChange={(e) => setOrderNumber(e.target.value)}
                placeholder="e.g., ORD-2024-001"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
              <p className="text-sm text-gray-500 mt-1">
                Providing your order number helps us assist you faster
              </p>
            </div>
          )}

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="low">Low - General question</option>
              <option value="normal">Normal - Standard issue</option>
              <option value="high">High - Urgent problem</option>
              <option value="critical">Critical - Service disruption</option>
            </select>
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Describe your issue
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={6}
              placeholder="Please provide as much detail as possible about your issue. Include any error messages, steps you've tried, and what you expected to happen."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              The more details you provide, the better we can help you
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500">
              We typically respond within 24 hours
            </p>
            <button
              type="submit"
              disabled={!selectedIssue || !message.trim()}
              className="flex items-center space-x-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              <PaperAirplaneIcon className="h-4 w-4" />
              <span>Send Message</span>
            </button>
          </div>
        </form>
      </div>

      {/* Support Hours */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex items-start space-x-3">
          <ClockIcon className="h-6 w-6 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900 mb-2">Support Hours</h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p><strong>Live Chat:</strong> 24/7 available</p>
              <p><strong>Phone Support:</strong> Monday - Friday, 8 AM - 8 PM EST</p>
              <p><strong>Email Support:</strong> 24/7 (responses within 24 hours)</p>
              <p><strong>Emergency Issues:</strong> 24/7 priority support available</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Links */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Links</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a
            href="/account/help"
            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <QuestionMarkCircleIcon className="h-5 w-5 text-gray-400" />
            <div>
              <p className="font-medium text-gray-900">Help Center</p>
              <p className="text-sm text-gray-500">Browse FAQs and guides</p>
            </div>
          </a>
          
          <a
            href="/account/orders"
            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <DocumentTextIcon className="h-5 w-5 text-gray-400" />
            <div>
              <p className="font-medium text-gray-900">Order History</p>
              <p className="text-sm text-gray-500">View your past orders</p>
            </div>
          </a>
          
          <a
            href="/account/orders/track"
            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ClockIcon className="h-5 w-5 text-gray-400" />
            <div>
              <p className="font-medium text-gray-900">Track Order</p>
              <p className="text-sm text-gray-500">Check your current order status</p>
            </div>
          </a>
          
          <a
            href="/account/payment"
            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-400" />
            <div>
              <p className="font-medium text-gray-900">Payment Methods</p>
              <p className="text-sm text-gray-500">Manage billing and payments</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
}
