'use client';

// /account/favorites route - redirects to main homepage with account view
import Home from '../../page';

export default function AccountFavoritesPage() {
  return <Home />;
}
import {
  HeartIcon,
  StarIcon,
  ClockIcon,
  TruckIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';

interface FavoriteRestaurant {
  id: string;
  name: string;
  image: string;
  cuisine: string;
  rating: number;
  reviewCount: number;
  deliveryTime: string;
  deliveryFee: number;
  minimumOrder: number;
  isOpen: boolean;
  distance: string;
  tags: string[];
  lastOrdered?: Date;
}

interface FavoriteItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  restaurantName: string;
  restaurantId: string;
  category: string;
  lastOrdered?: Date;
}

export default function Favorites() {
  const [favoriteRestaurants, setFavoriteRestaurants] = useState<FavoriteRestaurant[]>([]);
  const [favoriteItems, setFavoriteItems] = useState<FavoriteItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'restaurants' | 'items'>('restaurants');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent');

  useEffect(() => {
    // Simulate loading favorites
    setTimeout(() => {
      setFavoriteRestaurants([
        {
          id: '1',
          name: 'Pizza Palace',
          image: '/api/placeholder/300/200',
          cuisine: 'Italian',
          rating: 4.8,
          reviewCount: 1250,
          deliveryTime: '20-30 min',
          deliveryFee: 2.99,
          minimumOrder: 15.00,
          isOpen: true,
          distance: '1.2 km',
          tags: ['Pizza', 'Pasta', 'Italian'],
          lastOrdered: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        },
        {
          id: '2',
          name: 'Sushi Zen',
          image: '/api/placeholder/300/200',
          cuisine: 'Japanese',
          rating: 4.9,
          reviewCount: 890,
          deliveryTime: '25-35 min',
          deliveryFee: 3.99,
          minimumOrder: 20.00,
          isOpen: true,
          distance: '2.1 km',
          tags: ['Sushi', 'Japanese', 'Fresh'],
          lastOrdered: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        },
        {
          id: '3',
          name: 'Burger Junction',
          image: '/api/placeholder/300/200',
          cuisine: 'American',
          rating: 4.6,
          reviewCount: 2100,
          deliveryTime: '15-25 min',
          deliveryFee: 1.99,
          minimumOrder: 12.00,
          isOpen: false,
          distance: '0.8 km',
          tags: ['Burgers', 'Fast Food', 'American'],
          lastOrdered: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
        {
          id: '4',
          name: 'Taco Fiesta',
          image: '/api/placeholder/300/200',
          cuisine: 'Mexican',
          rating: 4.7,
          reviewCount: 756,
          deliveryTime: '15-25 min',
          deliveryFee: 1.99,
          minimumOrder: 10.00,
          isOpen: true,
          distance: '1.5 km',
          tags: ['Tacos', 'Mexican', 'Spicy'],
          lastOrdered: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
        },
      ]);

      setFavoriteItems([
        {
          id: '1',
          name: 'Margherita Pizza',
          description: 'Fresh mozzarella, tomato sauce, basil',
          price: 18.99,
          image: '/api/placeholder/150/150',
          restaurantName: 'Pizza Palace',
          restaurantId: '1',
          category: 'Pizza',
          lastOrdered: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        },
        {
          id: '2',
          name: 'California Roll',
          description: 'Crab, avocado, cucumber, sesame seeds',
          price: 8.99,
          image: '/api/placeholder/150/150',
          restaurantName: 'Sushi Zen',
          restaurantId: '2',
          category: 'Sushi',
          lastOrdered: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        },
        {
          id: '3',
          name: 'Classic Burger',
          description: 'Beef patty, lettuce, tomato, onion, pickles',
          price: 12.99,
          image: '/api/placeholder/150/150',
          restaurantName: 'Burger Junction',
          restaurantId: '3',
          category: 'Burgers',
          lastOrdered: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
        {
          id: '4',
          name: 'Chicken Tacos',
          description: 'Grilled chicken, salsa, cilantro, onions',
          price: 3.99,
          image: '/api/placeholder/150/150',
          restaurantName: 'Taco Fiesta',
          restaurantId: '4',
          category: 'Tacos',
          lastOrdered: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
        },
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  const removeFavoriteRestaurant = (restaurantId: string) => {
    setFavoriteRestaurants(prev => prev.filter(restaurant => restaurant.id !== restaurantId));
  };

  const removeFavoriteItem = (itemId: string) => {
    setFavoriteItems(prev => prev.filter(item => item.id !== itemId));
  };

  const filteredRestaurants = favoriteRestaurants.filter(restaurant =>
    restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    restaurant.cuisine.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredItems = favoriteItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.restaurantName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatLastOrdered = (date?: Date) => {
    if (!date) return 'Never ordered';
    const days = Math.floor((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    if (days < 30) return `${Math.floor(days / 7)} weeks ago`;
    return `${Math.floor(days / 30)} months ago`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Favorites</h1>
          <p className="text-gray-600">Your saved restaurants and favorite dishes.</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
            {favoriteRestaurants.length + favoriteItems.length} Favorites
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('restaurants')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'restaurants'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Restaurants ({favoriteRestaurants.length})
          </button>
          <button
            onClick={() => setActiveTab('items')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'items'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Dishes ({favoriteItems.length})
          </button>
        </nav>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder={`Search ${activeTab}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 w-full"
            />
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <AdjustmentsHorizontalIcon className="h-4 w-4 text-gray-400" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="recent">Recently Added</option>
                <option value="name">Name A-Z</option>
                <option value="rating">Highest Rated</option>
                <option value="lastOrdered">Last Ordered</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'restaurants' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRestaurants.map((restaurant) => (
            <div key={restaurant.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Restaurant Image</span>
                </div>
                <button
                  onClick={() => removeFavoriteRestaurant(restaurant.id)}
                  className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-shadow"
                >
                  <HeartSolidIcon className="h-5 w-5 text-red-500" />
                </button>
                {!restaurant.isOpen && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <span className="text-white font-medium">Closed</span>
                  </div>
                )}
              </div>
              
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-semibold text-gray-900">{restaurant.name}</h3>
                    <p className="text-sm text-gray-500">{restaurant.cuisine}</p>
                  </div>
                  <div className="flex items-center space-x-1">
                    <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium text-gray-900">{restaurant.rating}</span>
                    <span className="text-sm text-gray-500">({restaurant.reviewCount})</span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                  <div className="flex items-center space-x-1">
                    <ClockIcon className="h-4 w-4" />
                    <span>{restaurant.deliveryTime}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <TruckIcon className="h-4 w-4" />
                    <span>${restaurant.deliveryFee}</span>
                  </div>
                  <span>{restaurant.distance}</span>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-3">
                  {restaurant.tags.slice(0, 3).map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    Last ordered: {formatLastOrdered(restaurant.lastOrdered)}
                  </span>
                  <Link
                    href={`/restaurant/${restaurant.id}`}
                    className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Order Now
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <div key={item.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <div className="h-40 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Item Image</span>
                </div>
                <button
                  onClick={() => removeFavoriteItem(item.id)}
                  className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-shadow"
                >
                  <HeartSolidIcon className="h-5 w-5 text-red-500" />
                </button>
              </div>
              
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{item.name}</h3>
                    <p className="text-sm text-gray-500 mb-1">{item.description}</p>
                    <p className="text-xs text-gray-400">{item.restaurantName}</p>
                  </div>
                  <span className="font-bold text-gray-900">${item.price.toFixed(2)}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    Last ordered: {formatLastOrdered(item.lastOrdered)}
                  </span>
                  <button className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {((activeTab === 'restaurants' && filteredRestaurants.length === 0) ||
        (activeTab === 'items' && filteredItems.length === 0)) && (
        <div className="text-center py-12">
          <HeartIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No {activeTab} found
          </h3>
          <p className="text-gray-500 mb-6">
            {searchQuery 
              ? `No ${activeTab} match "${searchQuery}"`
              : `You haven't added any ${activeTab} to your favorites yet.`}
          </p>
          <Link
            href="/restaurants"
            className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Explore Restaurants
          </Link>
        </div>
      )}
    </div>
  );
}
