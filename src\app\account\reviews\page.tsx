'use client';

// /account/reviews route - redirects to main homepage with account view
import Home from '../../page';

export default function AccountReviewsPage() {
  return <Home />;
}
import {
  StarIcon,
  PencilIcon,
  TrashIcon,
  CameraIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

interface Review {
  id: string;
  restaurantName: string;
  restaurantId: string;
  orderNumber: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  date: Date;
  helpful: number;
  response?: {
    text: string;
    date: Date;
  };
}

export default function CustomerReviews() {
  const [reviews] = useState<Review[]>([
    {
      id: '1',
      restaurantName: 'Pizza Palace',
      restaurantId: '1',
      orderNumber: 'ORD-2024-001',
      rating: 5,
      title: 'Amazing pizza and fast delivery!',
      comment: 'The Margherita pizza was absolutely delicious. Fresh ingredients, perfect crust, and arrived hot. The garlic bread was also fantastic. Will definitely order again!',
      images: ['/api/placeholder/150/150', '/api/placeholder/150/150'],
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      helpful: 12,
      response: {
        text: 'Thank you so much for your wonderful review! We\'re thrilled you enjoyed our Margherita pizza. We look forward to serving you again soon!',
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      },
    },
    {
      id: '2',
      restaurantName: 'Burger Junction',
      restaurantId: '2',
      orderNumber: 'ORD-2024-002',
      rating: 4,
      title: 'Great burgers, could be warmer',
      comment: 'The Classic Burger was really tasty with fresh ingredients. However, it arrived a bit lukewarm. The fries were crispy and delicious though. Overall a good experience.',
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      helpful: 8,
    },
    {
      id: '3',
      restaurantName: 'Sushi Zen',
      restaurantId: '3',
      orderNumber: 'ORD-2024-003',
      rating: 5,
      title: 'Best sushi in town!',
      comment: 'Absolutely fresh and delicious sushi. The California rolls were perfect and the salmon sashimi melted in my mouth. Excellent presentation and packaging too.',
      images: ['/api/placeholder/150/150'],
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      helpful: 15,
      response: {
        text: 'Arigato gozaimasu! We\'re honored to be your favorite sushi place. Our chefs take great pride in using only the freshest ingredients.',
        date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
      },
    },
    {
      id: '4',
      restaurantName: 'Taco Fiesta',
      restaurantId: '4',
      orderNumber: 'ORD-2024-004',
      rating: 3,
      title: 'Decent tacos, room for improvement',
      comment: 'The chicken tacos were okay but could use more seasoning. The guacamole was fresh and tasty. Delivery was on time.',
      date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      helpful: 3,
    },
  ]);

  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const sizeClass = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5';
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`${sizeClass} ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
  const totalHelpful = reviews.reduce((sum, review) => sum + review.helpful, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Reviews</h1>
          <p className="text-gray-600">Share your dining experiences and help others discover great food.</p>
        </div>
        <button className="flex items-center space-x-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
          <PencilIcon className="h-4 w-4" />
          <span>Write Review</span>
        </button>
      </div>

      {/* Review Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Reviews</p>
              <p className="text-3xl font-bold text-gray-900">{reviews.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <PencilIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Average Rating</p>
              <div className="flex items-center space-x-2">
                <p className="text-3xl font-bold text-gray-900">{averageRating.toFixed(1)}</p>
                <div className="flex items-center">
                  {renderStars(Math.round(averageRating), 'md')}
                </div>
              </div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <StarIcon className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Helpful Votes</p>
              <p className="text-3xl font-bold text-gray-900">{totalHelpful}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <EyeIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-6">
        {reviews.map((review) => (
          <div key={review.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            {/* Review Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-600 font-medium text-sm">
                    {review.restaurantName.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{review.restaurantName}</h3>
                  <p className="text-sm text-gray-500">Order #{review.orderNumber}</p>
                  <p className="text-sm text-gray-500">{formatDate(review.date)}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Rating and Title */}
            <div className="mb-3">
              <div className="flex items-center space-x-2 mb-2">
                <div className="flex items-center">
                  {renderStars(review.rating)}
                </div>
                <span className="text-sm font-medium text-gray-700">{review.rating}/5</span>
              </div>
              <h4 className="font-medium text-gray-900">{review.title}</h4>
            </div>

            {/* Review Content */}
            <div className="mb-4">
              <p className="text-gray-700 leading-relaxed">{review.comment}</p>
            </div>

            {/* Review Images */}
            {review.images && review.images.length > 0 && (
              <div className="mb-4">
                <div className="flex space-x-2">
                  {review.images.map((image, index) => (
                    <div key={index} className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                      <CameraIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Restaurant Response */}
            {review.response && (
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="font-medium text-gray-900">Response from {review.restaurantName}</span>
                  <span className="text-sm text-gray-500">{formatDate(review.response.date)}</span>
                </div>
                <p className="text-gray-700">{review.response.text}</p>
              </div>
            )}

            {/* Review Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-1 text-gray-500 hover:text-gray-700 text-sm">
                  <span>👍</span>
                  <span>Helpful ({review.helpful})</span>
                </button>
                <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">
                  Share Review
                </button>
              </div>
              <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">
                Order Again
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {reviews.length === 0 && (
        <div className="text-center py-12">
          <StarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews yet</h3>
          <p className="text-gray-500 mb-6">
            Start sharing your dining experiences by writing your first review.
          </p>
          <button className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
            Write Your First Review
          </button>
        </div>
      )}

      {/* Review Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="font-medium text-blue-900 mb-3">📝 Review Guidelines</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <h4 className="font-medium mb-2">What makes a great review:</h4>
            <ul className="space-y-1">
              <li>• Be honest and specific about your experience</li>
              <li>• Mention food quality, delivery time, and packaging</li>
              <li>• Include photos when possible</li>
              <li>• Be respectful and constructive</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Earn rewards:</h4>
            <ul className="space-y-1">
              <li>• Get 15 loyalty points for each review</li>
              <li>• Bonus points for detailed reviews with photos</li>
              <li>• Help other customers make better choices</li>
              <li>• Build your reviewer reputation</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
